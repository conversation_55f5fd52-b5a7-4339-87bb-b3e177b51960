import { expect, test } from '@playwright/test';

test.describe('Math Formula Rendering in Quote Blocks', () => {
  test('should render formulas correctly after "题干本质" keyword in quote blocks', async ({
    page,
  }) => {
    // Navigate to the specific URL with the problematic content
    await page.goto('/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123');

    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');

    // Wait for MathJax to be available
    await page.waitForFunction(
      () => {
        return typeof window.MathJax !== 'undefined' && window.MathJax.typesetPromise;
      },
      { timeout: 30000 },
    );

    console.log('✅ MathJax is available');

    // Wait for the Shuxue component to be rendered
    await page.waitForSelector('.markdown-content', { timeout: 30000 });

    // Wait a bit more for content to be fully loaded and processed
    await page.waitForTimeout(3000);

    // Search for content containing "题干本质"
    const titleElements = await page.locator('text=题干本质').all();
    console.log(`🔍 Found ${titleElements.length} elements containing "题干本质"`);

    if (titleElements.length === 0) {
      console.log('⚠️ No elements found with "题干本质" keyword');
      // Still continue to check for quote blocks with formulas
    }

    // Find all blockquote elements that might contain formulas
    const blockquotes = await page.locator('blockquote').all();
    console.log(`📝 Found ${blockquotes.length} blockquote elements`);

    let formulasInQuotes = 0;
    let renderedFormulas = 0;

    for (let i = 0; i < blockquotes.length; i++) {
      const blockquote = blockquotes[i];

      // Check if this blockquote contains mathematical formulas
      const formulaPatterns = [
        /\$.*?\$/, // Inline math
        /\$\$.*?\$\$/, // Display math
        /\\\[.*?\\\]/, // LaTeX display math
        /\\\(.*?\\\)/, // LaTeX inline math
      ];

      const blockquoteText = await blockquote.textContent();
      const hasFormula = formulaPatterns.some((pattern) => pattern.test(blockquoteText));

      if (hasFormula) {
        formulasInQuotes++;
        console.log(
          `🧮 Blockquote ${i + 1} contains formulas: ${blockquoteText.substring(0, 100)}...`,
        );

        // Check if MathJax has rendered the formulas in this blockquote
        const mathContainers = await blockquote
          .locator('mjx-container, .MathJax, .tex2jax_process')
          .all();

        if (mathContainers.length > 0) {
          renderedFormulas++;
          console.log(
            `✅ Blockquote ${i + 1} has ${mathContainers.length} rendered math containers`,
          );

          // Verify that the math containers are visible and have content
          for (let j = 0; j < mathContainers.length; j++) {
            const container = mathContainers[j];
            const isVisible = await container.isVisible();
            const hasContent = (await container.textContent()).trim().length > 0;

            console.log(
              `   Math container ${j + 1}: visible=${isVisible}, hasContent=${hasContent}`,
            );
          }
        } else {
          console.log(`❌ Blockquote ${i + 1} has formulas but no rendered math containers`);

          // Log the raw HTML for debugging
          const innerHTML = await blockquote.innerHTML();
          console.log(`   Raw HTML: ${innerHTML.substring(0, 200)}...`);
        }
      }
    }

    console.log(
      `📊 Summary: ${formulasInQuotes} blockquotes with formulas, ${renderedFormulas} with rendered math`,
    );

    // Additional check: Look for any MathJax processing errors
    const mathJaxErrors = await page.evaluate(() => {
      const errors = [];
      if (window.MathJax && window.MathJax.startup && window.MathJax.startup.document) {
        const doc = window.MathJax.startup.document;
        if (doc.math && doc.math.length > 0) {
          doc.math.forEach((math, index) => {
            if (math.root && math.root.attributes && math.root.attributes.get('data-mjx-error')) {
              errors.push({
                index,
                error: math.root.attributes.get('data-mjx-error'),
                input: math.math,
              });
            }
          });
        }
      }
      return errors;
    });

    if (mathJaxErrors.length > 0) {
      console.log('❌ MathJax processing errors found:');
      mathJaxErrors.forEach((error, index) => {
        console.log(`   Error ${index + 1}: ${error.error} for input: ${error.input}`);
      });
    } else {
      console.log('✅ No MathJax processing errors detected');
    }

    // Main assertions
    expect(blockquotes.length).toBeGreaterThan(0); // Should have quote blocks

    if (formulasInQuotes > 0) {
      // If there are formulas in quotes, at least some should be rendered
      expect(renderedFormulas).toBeGreaterThan(0);
      console.log(
        `✅ Test passed: ${renderedFormulas}/${formulasInQuotes} blockquotes with formulas are properly rendered`,
      );
    } else {
      console.log('ℹ️ No formulas found in blockquotes to test');
    }

    // Ensure no MathJax errors
    expect(mathJaxErrors.length).toBe(0);
  });

  test('should handle MathJax initialization and rendering correctly', async ({ page }) => {
    // Navigate to the page
    await page.goto('/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123');

    // Wait for page load
    await page.waitForLoadState('networkidle');

    // Check MathJax configuration
    const mathJaxConfig = await page.evaluate(() => {
      if (typeof window.MathJax === 'undefined') {
        return { available: false };
      }

      return {
        available: true,
        version: window.MathJax.version || 'unknown',
        hasTypesetPromise: typeof window.MathJax.typesetPromise === 'function',
        texConfig: window.MathJax.config?.tex || window.MathJax.tex || {},
        startup: !!window.MathJax.startup,
      };
    });

    console.log('🧮 MathJax Configuration:', JSON.stringify(mathJaxConfig, null, 2));

    // Assertions for MathJax setup
    expect(mathJaxConfig.available).toBe(true);
    expect(mathJaxConfig.hasTypesetPromise).toBe(true);

    // Wait for Shuxue component
    await page.waitForSelector('.markdown-content.tex2jax_process', { timeout: 30000 });

    // Trigger MathJax rendering if needed
    await page.evaluate(async () => {
      if (window.MathJax && window.MathJax.typesetPromise) {
        await window.MathJax.typesetPromise();
      }
    });

    // Wait for rendering to complete
    await page.waitForTimeout(2000);

    // Check if any math was rendered
    const renderedMath = await page.locator('mjx-container, .MathJax').count();
    console.log(`🧮 Total rendered math elements: ${renderedMath}`);

    // The page should have the math processing class
    const hasProcessingClass = await page.locator('.tex2jax_process').count();
    expect(hasProcessingClass).toBeGreaterThan(0);
  });
});
