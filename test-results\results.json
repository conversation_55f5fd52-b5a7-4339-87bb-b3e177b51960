{"config": {"configFile": "I:\\workspace\\egg\\vue\\playwright.config.js", "rootDir": "I:/workspace/egg/vue/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "I:/workspace/egg/vue/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium", "name": "chromium", "testDir": "I:/workspace/egg/vue/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "I:/workspace/egg/vue/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "firefox", "name": "firefox", "testDir": "I:/workspace/egg/vue/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "I:/workspace/egg/vue/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "webkit", "name": "webkit", "testDir": "I:/workspace/egg/vue/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 8, "webServer": {"command": "pnpm dev", "url": "http://localhost:6001", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "math-formula-rendering.spec.js", "file": "math-formula-rendering.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Math Formula Rendering in Quote Blocks", "file": "math-formula-rendering.spec.js", "line": 3, "column": 6, "specs": [{"title": "should render formulas correctly after \"题干本质\" keyword in quote blocks", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 8918, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreater<PERSON>han\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n    at I:\\workspace\\egg\\vue\\tests\\math-formula-rendering.spec.js:134:32", "location": {"file": "I:\\workspace\\egg\\vue\\tests\\math-formula-rendering.spec.js", "column": 32, "line": 134}, "snippet": "\u001b[0m \u001b[90m 132 |\u001b[39m     \u001b[36mif\u001b[39m (formulasInQuotes \u001b[33m>\u001b[39m \u001b[35m0\u001b[39m) {\n \u001b[90m 133 |\u001b[39m       \u001b[90m// If there are formulas in quotes, at least some should be rendered\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 134 |\u001b[39m       expect(renderedFormulas)\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 135 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\n \u001b[90m 136 |\u001b[39m         \u001b[32m`✅ Test passed: ${renderedFormulas}/${formulasInQuotes} blockquotes with formulas are properly rendered`\u001b[39m\u001b[33m,\u001b[39m\n \u001b[90m 137 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "I:\\workspace\\egg\\vue\\tests\\math-formula-rendering.spec.js", "column": 32, "line": 134}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n\n\u001b[0m \u001b[90m 132 |\u001b[39m     \u001b[36mif\u001b[39m (formulasInQuotes \u001b[33m>\u001b[39m \u001b[35m0\u001b[39m) {\n \u001b[90m 133 |\u001b[39m       \u001b[90m// If there are formulas in quotes, at least some should be rendered\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 134 |\u001b[39m       expect(renderedFormulas)\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 135 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\n \u001b[90m 136 |\u001b[39m         \u001b[32m`✅ Test passed: ${renderedFormulas}/${formulasInQuotes} blockquotes with formulas are properly rendered`\u001b[39m\u001b[33m,\u001b[39m\n \u001b[90m 137 |\u001b[39m       )\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at I:\\workspace\\egg\\vue\\tests\\math-formula-rendering.spec.js:134:32\u001b[22m"}], "stdout": [{"text": "✅ MathJax is available\n"}, {"text": "🔍 Found 1 elements containing \"题干本质\"\n"}, {"text": "📝 Found 6 blockquote elements\n"}, {"text": "🧮 Blockquote 3 contains formulas: 题干本质：\n$\\color{red}{\\text{前组词关系} \\xrightarrow{\\text{必须镜像}} \\text{后组词关系}$\n选项筛选：\n1️⃣ 杀关系断裂：A（前属性后动作）\n2️...\n"}, {"text": "❌ Blockquote 3 has formulas but no rendered math containers\n"}, {"text": "   Raw HTML: <p><strong>题干本质</strong>：<br>\n$\\color{red}{\\text{前组词关系} \\xrightarrow{\\text{必须镜像}} \\text{后组词关系}$<br>\n<strong>选项筛选</strong>：<br>\n1️⃣ <strong>杀关系断裂</strong>：A（前属性后动作）<br>\n2️⃣ <strong>杀类型不符</strong>：B（前包含...\n"}, {"text": "📊 Summary: 1 blockquotes with formulas, 0 with rendered math\n"}, {"text": "✅ No MathJax processing errors detected\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T07:06:57.413Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "I:\\workspace\\egg\\vue\\test-results\\math-formula-rendering-Mat-0fcfe-干本质-keyword-in-quote-blocks-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "I:\\workspace\\egg\\vue\\test-results\\math-formula-rendering-Mat-0fcfe-干本质-keyword-in-quote-blocks-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "I:\\workspace\\egg\\vue\\test-results\\math-formula-rendering-Mat-0fcfe-干本质-keyword-in-quote-blocks-chromium\\error-context.md"}], "errorLocation": {"file": "I:\\workspace\\egg\\vue\\tests\\math-formula-rendering.spec.js", "column": 32, "line": 134}}], "status": "unexpected"}], "id": "d2cc4a1d923c7d13ad1c-ca7f088366ad61b999c3", "file": "math-formula-rendering.spec.js", "line": 4, "column": 3}, {"title": "should handle MathJax initialization and rendering correctly", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 8105, "errors": [], "stdout": [{"text": "🧮 MathJax Configuration: {\n  \"available\": true,\n  \"version\": \"3.2.2\",\n  \"hasTypesetPromise\": true,\n  \"texConfig\": {\n    \"inlineMath\": [\n      [\n        \"$\",\n        \"$\"\n      ],\n      [\n        \"\\\\(\",\n        \"\\\\)\"\n      ]\n    ],\n    \"displayMath\": [\n      [\n        \"$$\",\n        \"$$\"\n      ],\n      [\n        \"\\\\[\",\n        \"\\\\]\"\n      ]\n    ],\n    \"processEscapes\": true,\n    \"packages\": [\n      \"require\",\n      \"base\",\n      \"action\",\n      \"ams\",\n      \"amscd\",\n      \"bbox\",\n      \"boldsymbol\",\n      \"braket\",\n      \"bussproofs\",\n      \"cancel\",\n      \"cases\",\n      \"centernot\",\n      \"color\",\n      \"colortbl\",\n      \"empheq\",\n      \"enclose\",\n      \"extpfeil\",\n      \"gensymb\",\n      \"html\",\n      \"mathtools\",\n      \"mhchem\",\n      \"newcommand\",\n      \"noerrors\",\n      \"noundefined\",\n      \"upgreek\",\n      \"unicode\",\n      \"verb\",\n      \"configmacros\",\n      \"tagformat\",\n      \"textcomp\",\n      \"textmacros\",\n      \"ams\",\n      \"color\",\n      \"cancel\",\n      \"boldsymbol\",\n      \"textmacros\"\n    ],\n    \"tags\": \"ams\"\n  },\n  \"startup\": true\n}\n"}, {"text": "🧮 Total rendered math elements: 9\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T07:06:57.273Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d2cc4a1d923c7d13ad1c-341dd4eaccd47fb27080", "file": "math-formula-rendering.spec.js", "line": 146, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-31T07:06:56.360Z", "duration": 11248.375, "expected": 1, "skipped": 0, "unexpected": 1, "flaky": 0}}