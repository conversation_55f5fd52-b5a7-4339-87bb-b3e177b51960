{"config": {"configFile": "I:\\workspace\\egg\\vue\\playwright.config.js", "rootDir": "I:/workspace/egg/vue/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "I:/workspace/egg/vue/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium", "name": "chromium", "testDir": "I:/workspace/egg/vue/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "I:/workspace/egg/vue/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "firefox", "name": "firefox", "testDir": "I:/workspace/egg/vue/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "I:/workspace/egg/vue/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "webkit", "name": "webkit", "testDir": "I:/workspace/egg/vue/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 8, "webServer": {"command": "pnpm dev", "url": "http://localhost:6001", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "math-formula-rendering.spec.js", "file": "math-formula-rendering.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Math Formula Rendering in Quote Blocks", "file": "math-formula-rendering.spec.js", "line": 3, "column": 6, "specs": [{"title": "should render formulas correctly after \"题干本质\" keyword in quote blocks", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 8621, "errors": [], "stdout": [{"text": "✅ MathJax is available\n"}, {"text": "🔍 Found 1 elements containing \"题干本质\"\n"}, {"text": "📝 Found 6 blockquote elements\n"}, {"text": "🧮 Blockquote 3 contains formulas: 题干本质：\n$\\color{red}{\\text{前组词关系} \\xrightarrow{\\text{必须镜像}} \\text{后组词关系}$\n选项筛选：\n1️⃣ 杀关系断裂：A（前属性后动作）\n2️...\n"}, {"text": "✅ Blockquote 3 has 2 rendered math containers\n"}, {"text": "   Math container 1: visible=true, hasContent=true\n"}, {"text": "   Math container 2: visible=true, hasContent=true\n"}, {"text": "📊 Summary: 1 blockquotes with formulas, 1 with rendered math\n"}, {"text": "✅ No MathJax processing errors detected\n"}, {"text": "✅ Test passed: 1/1 blockquotes with formulas are properly rendered\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T07:10:32.583Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d2cc4a1d923c7d13ad1c-ca7f088366ad61b999c3", "file": "math-formula-rendering.spec.js", "line": 4, "column": 3}, {"title": "should handle MathJax initialization and rendering correctly", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 7640, "errors": [], "stdout": [{"text": "🧮 MathJax Configuration: {\n  \"available\": true,\n  \"version\": \"3.2.2\",\n  \"hasTypesetPromise\": true,\n  \"texConfig\": {\n    \"inlineMath\": [\n      [\n        \"$\",\n        \"$\"\n      ],\n      [\n        \"\\\\(\",\n        \"\\\\)\"\n      ]\n    ],\n    \"displayMath\": [\n      [\n        \"$$\",\n        \"$$\"\n      ],\n      [\n        \"\\\\[\",\n        \"\\\\]\"\n      ]\n    ],\n    \"processEscapes\": true,\n    \"packages\": [\n      \"require\",\n      \"base\",\n      \"action\",\n      \"ams\",\n      \"amscd\",\n      \"bbox\",\n      \"boldsymbol\",\n      \"braket\",\n      \"bussproofs\",\n      \"cancel\",\n      \"cases\",\n      \"centernot\",\n      \"color\",\n      \"colortbl\",\n      \"empheq\",\n      \"enclose\",\n      \"extpfeil\",\n      \"gensymb\",\n      \"html\",\n      \"mathtools\",\n      \"mhchem\",\n      \"newcommand\",\n      \"noerrors\",\n      \"noundefined\",\n      \"upgreek\",\n      \"unicode\",\n      \"verb\",\n      \"configmacros\",\n      \"tagformat\",\n      \"textcomp\",\n      \"textmacros\",\n      \"ams\",\n      \"color\",\n      \"cancel\",\n      \"boldsymbol\",\n      \"textmacros\"\n    ],\n    \"tags\": \"ams\"\n  },\n  \"startup\": true\n}\n"}, {"text": "🧮 Total rendered math elements: 9\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T07:10:32.706Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d2cc4a1d923c7d13ad1c-341dd4eaccd47fb27080", "file": "math-formula-rendering.spec.js", "line": 146, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-31T07:10:31.713Z", "duration": 10943.615, "expected": 2, "skipped": 0, "unexpected": 0, "flaky": 0}}